'use client';

import React from 'react';

interface ScoreData {
  listening?: number | null;
  reading?: number | null;
  writing?: number | null;
  speaking?: number | null;
  overall?: number | null;
}

interface ScoreChartProps {
  scores: ScoreData;
  className?: string;
}

export default function ScoreChart({ scores, className = '' }: ScoreChartProps) {
  const modules = [
    { name: 'Listening', score: scores.listening, color: 'bg-blue-500' },
    { name: 'Reading', score: scores.reading, color: 'bg-green-500' },
    { name: 'Writing', score: scores.writing, color: 'bg-yellow-500' },
    { name: 'Speaking', score: scores.speaking, color: 'bg-purple-500' },
  ].filter(module => module.score !== null && module.score !== undefined);

  const maxScore = 9;

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Score Breakdown</h3>

      <div className="space-y-4">
        {modules.length > 0 ? (
          modules.map((module) => (
            <div key={module.name} className="flex items-center">
              <div className="w-20 text-sm font-medium text-gray-700">
                {module.name}
              </div>
              <div className="flex-1 mx-4">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full ${module.color} transition-all duration-500 ease-out`}
                    style={{
                      width: module.score ? `${(module.score / maxScore) * 100}%` : '0%'
                    }}
                  ></div>
                </div>
              </div>
              <div className="w-12 text-right">
                <span className="text-lg font-bold text-gray-900">
                  {module.score}
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No module scores available
          </div>
        )}

        {/* Overall Score */}
        {scores.overall && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="w-20 text-sm font-medium text-gray-700">
                Overall
              </div>
              <div className="flex-1 mx-4">
                <div className="w-full bg-gray-200 rounded-full h-4">
                  <div
                    className="h-4 rounded-full bg-indigo-600 transition-all duration-500 ease-out"
                    style={{
                      width: `${(scores.overall / maxScore) * 100}%`
                    }}
                  ></div>
                </div>
              </div>
              <div className="w-12 text-right">
                <span className="text-xl font-bold text-indigo-600">
                  {scores.overall}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
