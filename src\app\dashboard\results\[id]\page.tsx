'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Edit,
  Download,
  User,
  Calendar,
  MapPin,
  FileText,
  Headphones,
  BookOpen,
  PenTool,
  Mic,
  BarChart3,
  Brain,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface TestResult {
  id: string;
  candidateId: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  candidate: {
    fullName: string;
    email: string;
    phoneNumber: string;
    passportNumber: string;
    nationality: string;
    testDate: string;
    testCenter: string;
    photoUrl?: string;
  };
}

export default function ResultDetailPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isGeneratingCertificate, setIsGeneratingCertificate] = useState(false);

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/checker/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        setError('Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    if (resultId) {
      fetchResult();
    }
  }, [resultId, fetchResult]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      default:
        return `${baseClasses} bg-red-100 text-red-800`;
    }
  };

  const generateCertificate = async () => {
    if (!result) return;

    setIsGeneratingCertificate(true);
    try {
      const response = await fetch(`/api/certificate/${resultId}`, {
        method: 'POST',
      });

      if (response.ok) {
        // Download the certificate
        const downloadResponse = await fetch(`/api/certificate/${resultId}`);
        if (downloadResponse.ok) {
          const blob = await downloadResponse.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `IELTS_Certificate_${result.candidate.fullName.replace(/\s+/g, '_')}_${resultId}.pdf`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);

          // Update the result state to reflect certificate generation
          setResult(prev => prev ? { ...prev, certificateGenerated: true } : null);
        }
      } else {
        const errorData = await response.json();
        alert(`Failed to generate certificate: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Certificate generation error:', error);
      alert('An error occurred while generating the certificate');
    } finally {
      setIsGeneratingCertificate(false);
    }
  };

  const downloadCertificate = async () => {
    try {
      const response = await fetch(`/api/certificate/${resultId}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `IELTS_Certificate_${result?.candidate.fullName.replace(/\s+/g, '_')}_${resultId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Certificate download error:', error);
      alert('An error occurred while downloading the certificate');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Result</h3>
        <p className="text-gray-600">{error || 'Result not found'}</p>
        <Link
          href="/dashboard/results/list"
          className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Results
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/dashboard/results/list"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Test Result Details</h1>
            <p className="text-gray-600">View complete IELTS test results</p>
          </div>
        </div>
        <div className="flex gap-3">
          <Link
            href={`/dashboard/results/${resultId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Results
          </Link>
          {result.certificateGenerated ? (
            <button
              onClick={downloadCertificate}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Certificate
            </button>
          ) : (
            <button
              onClick={generateCertificate}
              disabled={isGeneratingCertificate || result.status === 'pending'}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isGeneratingCertificate ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Generate Certificate
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Candidate Information */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <User className="h-5 w-5 mr-2" />
          Candidate Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center space-x-4">
            {result.candidate.photoUrl ? (
              <Image
                className="h-16 w-16 rounded-full object-cover"
                src={`/api/candidates/${result.candidate.id}/photo`}
                alt={result.candidate.fullName}
                width={64}
                height={64}
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                <User className="h-8 w-8 text-gray-400" />
              </div>
            )}
            <div>
              <h4 className="text-xl font-semibold text-gray-900">{result.candidate.fullName}</h4>
              <p className="text-gray-600">{result.candidate.email}</p>
              <p className="text-gray-600">{result.candidate.phoneNumber}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <FileText className="h-4 w-4 mr-2" />
              Passport: {result.candidate.passportNumber}
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="h-4 w-4 mr-2" />
              Test Center: {result.candidate.testCenter}
            </div>
          </div>
        </div>
      </div>

      {/* Result Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {getStatusIcon(result.status)}
            <span className={`ml-3 ${getStatusBadge(result.status)}`}>
              {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
            </span>
          </div>
          <div className="text-sm text-gray-500">
            Entered: {new Date(result.createdAt).toLocaleDateString()}
            {result.updatedAt !== result.createdAt && (
              <span className="ml-2">
                • Updated: {new Date(result.updatedAt).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Test Scores */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Individual Skills */}
        <div className="space-y-6">
          {/* Listening */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Headphones className="h-5 w-5 mr-2 text-blue-600" />
              Listening
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Raw Score</p>
                <p className="text-2xl font-bold text-gray-900">
                  {result.listeningScore || 'N/A'}/40
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Band Score</p>
                <p className="text-2xl font-bold text-blue-600">
                  {result.listeningBandScore || 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Reading */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-green-600" />
              Reading
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Raw Score</p>
                <p className="text-2xl font-bold text-gray-900">
                  {result.readingScore || 'N/A'}/40
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Band Score</p>
                <p className="text-2xl font-bold text-green-600">
                  {result.readingBandScore || 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Writing */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <PenTool className="h-5 w-5 mr-2 text-purple-600" />
              Writing
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">Task 1</p>
                <p className="text-xl font-bold text-gray-900">
                  {result.writingTask1Score || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Task 2</p>
                <p className="text-xl font-bold text-gray-900">
                  {result.writingTask2Score || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Band Score</p>
                <p className="text-xl font-bold text-purple-600">
                  {result.writingBandScore || 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Speaking */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Mic className="h-5 w-5 mr-2 text-red-600" />
              Speaking
            </h3>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500">Fluency & Coherence</p>
                <p className="text-lg font-bold text-gray-900">
                  {result.speakingFluencyScore || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Lexical Resource</p>
                <p className="text-lg font-bold text-gray-900">
                  {result.speakingLexicalScore || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Grammar & Accuracy</p>
                <p className="text-lg font-bold text-gray-900">
                  {result.speakingGrammarScore || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Pronunciation</p>
                <p className="text-lg font-bold text-gray-900">
                  {result.speakingPronunciationScore || 'N/A'}
                </p>
              </div>
            </div>
            <div className="border-t pt-4">
              <p className="text-sm text-gray-500">Overall Band Score</p>
              <p className="text-2xl font-bold text-red-600">
                {result.speakingBandScore || 'N/A'}
              </p>
            </div>
          </div>
        </div>

        {/* Overall Score & Actions */}
        <div className="space-y-6">
          {/* Overall Band Score */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-100 shadow rounded-lg p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center justify-center">
              <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
              Overall Band Score
            </h3>
            <div className="text-6xl font-bold text-indigo-600 mb-2">
              {result.overallBandScore || 'N/A'}
            </div>
            <p className="text-gray-600">IELTS Band Score</p>
          </div>

          {/* Quick Actions */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Actions</h3>
            <div className="space-y-3">
              <Link
                href={`/dashboard/feedback/generate?resultId=${resultId}`}
                className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <Brain className="h-4 w-4 mr-2" />
                Generate AI Feedback
              </Link>
              {result.certificateGenerated ? (
                <button
                  onClick={downloadCertificate}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Certificate
                </button>
              ) : (
                <button
                  onClick={generateCertificate}
                  disabled={isGeneratingCertificate || result.status === 'pending'}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  {isGeneratingCertificate ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      Generating...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Generate Certificate
                    </>
                  )}
                </button>
              )}
              <Link
                href={`/dashboard/results/${resultId}/edit`}
                className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Results
              </Link>
            </div>
          </div>

          {/* Score Summary */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Score Summary</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Listening</span>
                <span className="font-medium">{result.listeningBandScore || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Reading</span>
                <span className="font-medium">{result.readingBandScore || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Writing</span>
                <span className="font-medium">{result.writingBandScore || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Speaking</span>
                <span className="font-medium">{result.speakingBandScore || 'N/A'}</span>
              </div>
              <div className="border-t pt-3 flex justify-between">
                <span className="font-medium text-gray-900">Overall</span>
                <span className="font-bold text-indigo-600">{result.overallBandScore || 'N/A'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
