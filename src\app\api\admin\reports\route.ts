import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, gte, lte, and, count, avg, sql } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { from, to } = await request.json();
    const fromDate = new Date(from);
    const toDate = new Date(to);

    // Overview statistics
    const totalCandidatesResult = await db
      .select({ count: count() })
      .from(candidates)
      .where(and(
        gte(candidates.createdAt, fromDate),
        lte(candidates.createdAt, toDate)
      ));

    const totalResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(and(
        eq(testResults.status, 'completed'),
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ));

    const averageBandScoreResult = await db
      .select({ avg: avg(testResults.overallBandScore) })
      .from(testResults)
      .where(and(
        eq(testResults.status, 'completed'),
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ));

    const certificatesGeneratedResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(and(
        eq(testResults.certificateGenerated, true),
        eq(testResults.status, 'completed'),
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ));

    // Band score distribution (only completed results)
    const bandScoreDistribution = await db
      .select({
        score: testResults.overallBandScore,
        count: count(),
      })
      .from(testResults)
      .where(and(
        eq(testResults.status, 'completed'),
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ))
      .groupBy(testResults.overallBandScore)
      .orderBy(testResults.overallBandScore);

    // Calculate percentages for band score distribution
    const totalForDistribution = bandScoreDistribution.reduce((sum, item) => sum + item.count, 0);
    const processedBandDistribution = bandScoreDistribution
      .filter(item => item.score !== null)
      .map(item => ({
        score: item.score!.toString(),
        count: item.count,
        percentage: totalForDistribution > 0 ? (item.count / totalForDistribution) * 100 : 0,
      }));

    // Monthly statistics
    const monthlyStats = await db
      .select({
        month: sql<string>`TO_CHAR(${candidates.createdAt}, 'YYYY-MM')`,
        candidates: count(candidates.id),
        results: count(sql`CASE WHEN ${testResults.status} = 'completed' THEN ${testResults.id} END`),
        avgScore: avg(sql`CASE WHEN ${testResults.status} = 'completed' THEN ${testResults.overallBandScore} END`),
      })
      .from(candidates)
      .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
      .where(and(
        gte(candidates.createdAt, fromDate),
        lte(candidates.createdAt, toDate)
      ))
      .groupBy(sql`TO_CHAR(${candidates.createdAt}, 'YYYY-MM')`)
      .orderBy(sql`TO_CHAR(${candidates.createdAt}, 'YYYY-MM')`);

    // Recent Sunday activity (last 8 weeks of Sundays)
    const recentActivity = [];

    // Find the most recent Sunday
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const mostRecentSunday = new Date(today);
    mostRecentSunday.setDate(today.getDate() - dayOfWeek);

    for (let i = 7; i >= 0; i--) {
      const sunday = new Date(mostRecentSunday);
      sunday.setDate(mostRecentSunday.getDate() - (i * 7));

      const startOfSunday = new Date(sunday.getFullYear(), sunday.getMonth(), sunday.getDate());
      const endOfSunday = new Date(sunday.getFullYear(), sunday.getMonth(), sunday.getDate() + 1);

      const candidatesCount = await db
        .select({ count: count() })
        .from(candidates)
        .where(and(
          gte(candidates.testDate, startOfSunday),
          lte(candidates.testDate, endOfSunday)
        ));

      const resultsCount = await db
        .select({ count: count() })
        .from(testResults)
        .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
        .where(and(
          eq(testResults.status, 'completed'),
          gte(candidates.testDate, startOfSunday),
          lte(candidates.testDate, endOfSunday)
        ));

      recentActivity.push({
        date: startOfSunday.toISOString().split('T')[0],
        candidates: candidatesCount[0]?.count || 0,
        results: resultsCount[0]?.count || 0,
      });
    }

    const reportData = {
      overview: {
        totalCandidates: totalCandidatesResult[0]?.count || 0,
        totalResults: totalResultsResult[0]?.count || 0,
        averageBandScore: Number(averageBandScoreResult[0]?.avg) || 0,
        certificatesGenerated: certificatesGeneratedResult[0]?.count || 0,
      },
      monthlyStats: monthlyStats.map(stat => ({
        month: stat.month,
        candidates: stat.candidates,
        results: stat.results,
        avgScore: Number(stat.avgScore) || 0,
      })),
      bandScoreDistribution: processedBandDistribution,
      recentActivity,
    };

    return NextResponse.json(reportData);

  } catch (error) {
    console.error('Reports error:', error);
    return NextResponse.json(
      { error: 'Failed to generate reports' },
      { status: 500 }
    );
  }
}
