'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  Download,
  Calendar,
  MapPin,
  User,
  FileText,
  Brain,
  Award,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  <PERSON>O<PERSON>,
  TrendingUp
} from 'lucide-react';
import Score<PERSON>hart from '@/components/charts/ScoreChart';
import PerformanceChart from '@/components/charts/PerformanceChart';

interface TestResult {
  id: string;
  candidateId: string;
  testDate: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial: string | null;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  candidate: {
    fullName: string;
    nationality: string;
    testDate: string;
    testCenter: string;
    photoUrl: string | null;
  };
  performanceMetrics: {
    averageScore: number | null;
    highestScore: number | null;
    lowestScore: number | null;
    scoreDistribution: {
      listening?: number | null;
      reading?: number | null;
      writing?: number | null;
      speaking?: number | null;
    };
  };
}

interface AIFeedback {
  id: string;
  testResultId: string;
  listeningFeedback: string | null;
  readingFeedback: string | null;
  writingFeedback: string | null;
  speakingFeedback: string | null;
  overallFeedback: string | null;
  studyRecommendations: string | null;
  strengths: string[] | null;
  weaknesses: string[] | null;
  studyPlan: {
    timeframe: string;
    focusAreas: string[];
    resources: string[];
    practiceActivities: string[];
  } | null;
  generatedAt: string;
}

export default function PublicResultsPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [feedback, setFeedback] = useState<AIFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'scores' | 'feedback'>('scores');

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  const fetchFeedback = useCallback(async () => {
    if (!result?.aiFeedbackGenerated) return;

    setFeedbackLoading(true);
    try {
      const response = await fetch(`/api/feedback/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setFeedback(data);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
    } finally {
      setFeedbackLoading(false);
    }
  }, [resultId, result?.aiFeedbackGenerated]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  useEffect(() => {
    if (result) {
      fetchFeedback();
    }
  }, [fetchFeedback, result]);

  const downloadCertificate = async () => {
    try {
      const response = await fetch(`/api/certificate/${resultId}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `IELTS_Certificate_${result?.candidate.fullName.replace(/\s+/g, '_')}_${resultId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Certificate download error:', error);
      alert('An error occurred while downloading the certificate');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'verified':
        return <Award className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-800 bg-yellow-100';
      case 'completed':
        return 'text-green-800 bg-green-100';
      case 'verified':
        return 'text-blue-800 bg-blue-100';
      default:
        return 'text-gray-800 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your results...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Unable to Load Results</h3>
          <p className="text-gray-600 mb-6">{error || 'Result not found'}</p>
          <Link
            href="/search"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <FileText className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">IELTS Test Results</h1>
                <p className="text-gray-600">Official Test Report</p>
              </div>
            </div>
            {result.certificateGenerated && (
              <button
                onClick={downloadCertificate}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Certificate
              </button>
            )}
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Candidate Info & Status */}
          <div className="lg:col-span-1 space-y-6">
            {/* Candidate Information */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Candidate Information</h2>
              <div className="flex items-start space-x-4">
                {result.candidate.photoUrl ? (
                  <Image
                    src={result.candidate.photoUrl}
                    alt={result.candidate.fullName}
                    width={80}
                    height={80}
                    className="rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                    <User className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900">{result.candidate.fullName}</h4>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      {result.candidate.nationality}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      Test Center: {result.candidate.testCenter}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Result Status */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Result Status</h2>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {getStatusIcon(result.status)}
                  <span className={`ml-3 px-3 py-1 rounded-full text-sm font-medium ${getStatusBadge(result.status)}`}>
                    {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="mt-4 text-sm text-gray-600">
                <p>Result ID: {result.id}</p>
                {result.certificateSerial && (
                  <p>Certificate Serial: {result.certificateSerial}</p>
                )}
                <p>Generated: {new Date(result.createdAt).toLocaleDateString()}</p>
              </div>
            </div>

            {/* Overall Score */}
            <div className="bg-gradient-to-br from-indigo-500 to-purple-600 shadow rounded-lg p-6 text-white text-center">
              <h2 className="text-lg font-medium mb-4">Overall Band Score</h2>
              <div className="text-5xl font-bold mb-2">
                {result.overallBandScore || 'N/A'}
              </div>
              <p className="text-indigo-100">IELTS Band Score</p>
              {result.overallBandScore && (
                <div className="mt-4 text-sm">
                  {result.overallBandScore >= 8.5 && <p>Expert User</p>}
                  {result.overallBandScore >= 7.5 && result.overallBandScore < 8.5 && <p>Very Good User</p>}
                  {result.overallBandScore >= 6.5 && result.overallBandScore < 7.5 && <p>Good User</p>}
                  {result.overallBandScore >= 5.5 && result.overallBandScore < 6.5 && <p>Modest User</p>}
                  {result.overallBandScore < 5.5 && <p>Limited User</p>}
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Tabbed Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Tab Navigation */}
            <div className="bg-white shadow rounded-lg">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                  <button
                    onClick={() => setActiveTab('scores')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'scores'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2" />
                      Test Scores & Analysis
                    </div>
                  </button>
                  {result.aiFeedbackGenerated && (
                    <button
                      onClick={() => setActiveTab('feedback')}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'feedback'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center">
                        <Brain className="h-5 w-5 mr-2" />
                        AI Feedback & Study Plan
                      </div>
                    </button>
                  )}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'scores' && (
                  <div className="space-y-6">
                    {/* Score Charts */}
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                      <ScoreChart
                        scores={{
                          listening: result.listeningBandScore,
                          reading: result.readingBandScore,
                          writing: result.writingBandScore,
                          speaking: result.speakingBandScore,
                          overall: result.overallBandScore,
                        }}
                      />
                      <PerformanceChart
                        metrics={result.performanceMetrics}
                        overallScore={result.overallBandScore}
                      />
                    </div>

                    {/* Enhanced Score Breakdown */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-6">Detailed Score Breakdown</h3>

              <div className="space-y-6">
                {/* Listening */}
                {result.listeningBandScore && (
                  <div className="border border-blue-200 rounded-lg p-6 bg-blue-50/30">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-blue-600 flex items-center">
                        <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        Listening
                      </h3>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-blue-600">{result.listeningBandScore}</div>
                        <div className="text-sm text-gray-600">Band Score</div>
                      </div>
                    </div>

                    {result.listeningScore && (
                      <div className="mb-3">
                        <span className="text-sm text-gray-600">Raw Score: </span>
                        <span className="font-medium">{result.listeningScore}/40</span>
                      </div>
                    )}


                  </div>
                )}

                {/* Reading */}
                {result.readingBandScore && (
                  <div className="border border-green-200 rounded-lg p-6 bg-green-50/30">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-green-600 flex items-center">
                        <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        Reading
                      </h3>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-green-600">{result.readingBandScore}</div>
                        <div className="text-sm text-gray-600">Band Score</div>
                      </div>
                    </div>

                    {result.readingScore && (
                      <div className="mb-3">
                        <span className="text-sm text-gray-600">Raw Score: </span>
                        <span className="font-medium">{result.readingScore}/40</span>
                      </div>
                    )}


                  </div>
                )}

                {/* Writing */}
                {result.writingBandScore && (
                  <div className="border border-yellow-200 rounded-lg p-6 bg-yellow-50/30">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-yellow-600 flex items-center">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                        Writing
                      </h3>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-yellow-600">{result.writingBandScore}</div>
                        <div className="text-sm text-gray-600">Band Score</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-3">
                      {result.writingTask1Score && (
                        <div>
                          <span className="text-sm text-gray-600">Task 1: </span>
                          <span className="font-medium">{result.writingTask1Score}/9</span>
                        </div>
                      )}
                      {result.writingTask2Score && (
                        <div>
                          <span className="text-sm text-gray-600">Task 2: </span>
                          <span className="font-medium">{result.writingTask2Score}/9</span>
                        </div>
                      )}
                    </div>


                  </div>
                )}

                {/* Speaking */}
                {result.speakingBandScore && (
                  <div className="border border-purple-200 rounded-lg p-6 bg-purple-50/30">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-purple-600 flex items-center">
                        <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                        Speaking
                      </h3>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-purple-600">{result.speakingBandScore}</div>
                        <div className="text-sm text-gray-600">Band Score</div>
                      </div>
                    </div>


                  </div>
                )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Feedback Tab */}
                {activeTab === 'feedback' && result.aiFeedbackGenerated && (
                  <div className="space-y-6">
                    {/* Loading State */}
                    {feedbackLoading && (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">Loading AI insights...</p>
                      </div>
                    )}

                    {/* No Feedback Available */}
                    {!feedbackLoading && !feedback && (
                      <div className="text-center py-8">
                        <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">AI feedback is not available at this time.</p>
                      </div>
                    )}

                    {/* Feedback Content */}
                    {feedback && (
              <div className="space-y-6">
                {/* Overall Assessment */}
                {feedback.overallFeedback && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <Brain className="h-6 w-6 text-purple-600 mr-2" />
                      <h2 className="text-lg font-medium text-gray-900">Overall Assessment</h2>
                    </div>
                    <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6">
                      <p className="text-gray-700 leading-relaxed">{feedback.overallFeedback}</p>
                    </div>
                  </div>
                )}

                {/* Strengths and Areas for Improvement */}
                {(feedback.strengths || feedback.weaknesses) && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-6">Performance Analysis</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Strengths */}
                      {feedback.strengths && feedback.strengths.length > 0 && (
                        <div className="bg-green-50 rounded-lg p-6">
                          <h3 className="font-semibold text-green-700 mb-4 flex items-center">
                            <CheckCircle className="h-5 w-5 mr-2" />
                            Key Strengths
                          </h3>
                          <ul className="space-y-2">
                            {feedback.strengths.map((strength, index) => (
                              <li key={index} className="flex items-start">
                                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <span className="text-green-800 text-sm">{strength}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Areas for Improvement */}
                      {feedback.weaknesses && feedback.weaknesses.length > 0 && (
                        <div className="bg-orange-50 rounded-lg p-6">
                          <h3 className="font-semibold text-orange-700 mb-4 flex items-center">
                            <AlertCircle className="h-5 w-5 mr-2" />
                            Areas for Improvement
                          </h3>
                          <ul className="space-y-2">
                            {feedback.weaknesses.map((weakness, index) => (
                              <li key={index} className="flex items-start">
                                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                <span className="text-orange-800 text-sm">{weakness}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Study Plan */}
                {feedback.studyPlan && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
                      <FileText className="h-6 w-6 text-blue-600 mr-2" />
                      Personalized Study Plan
                    </h2>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Timeframe and Focus Areas */}
                      <div className="space-y-4">
                        {feedback.studyPlan.timeframe && (
                          <div className="bg-blue-50 rounded-lg p-4">
                            <h4 className="font-semibold text-blue-700 mb-2 flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              Recommended Timeframe
                            </h4>
                            <p className="text-blue-800 text-sm">{feedback.studyPlan.timeframe}</p>
                          </div>
                        )}

                        {feedback.studyPlan.focusAreas && feedback.studyPlan.focusAreas.length > 0 && (
                          <div className="bg-indigo-50 rounded-lg p-4">
                            <h4 className="font-semibold text-indigo-700 mb-3">Priority Focus Areas</h4>
                            <ul className="space-y-1">
                              {feedback.studyPlan.focusAreas.map((area, index) => (
                                <li key={index} className="flex items-center text-sm text-indigo-800">
                                  <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full mr-2"></div>
                                  {area}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      {/* Resources and Practice Activities */}
                      <div className="space-y-4">
                        {feedback.studyPlan.resources && feedback.studyPlan.resources.length > 0 && (
                          <div className="bg-emerald-50 rounded-lg p-4">
                            <h4 className="font-semibold text-emerald-700 mb-3">Recommended Resources</h4>
                            <ul className="space-y-1">
                              {feedback.studyPlan.resources.map((resource, index) => (
                                <li key={index} className="flex items-center text-sm text-emerald-800">
                                  <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2"></div>
                                  {resource}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {feedback.studyPlan.practiceActivities && feedback.studyPlan.practiceActivities.length > 0 && (
                          <div className="bg-amber-50 rounded-lg p-4">
                            <h4 className="font-semibold text-amber-700 mb-3">Practice Activities</h4>
                            <ul className="space-y-1">
                              {feedback.studyPlan.practiceActivities.map((activity, index) => (
                                <li key={index} className="flex items-center text-sm text-amber-800">
                                  <div className="w-1.5 h-1.5 bg-amber-500 rounded-full mr-2"></div>
                                  {activity}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Study Recommendations */}
                {feedback.studyRecommendations && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Additional Study Recommendations</h2>
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
                      <p className="text-gray-700 leading-relaxed">{feedback.studyRecommendations}</p>
                    </div>
                  </div>
                )}

                        <div className="text-center">
                          <div className="text-xs text-gray-500">
                            AI feedback generated on {new Date(feedback.generatedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 bg-white shadow rounded-lg p-6 text-center">
          <div className="flex items-center justify-center mb-4">
            <Award className="h-8 w-8 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Official IELTS Test Report</h3>
          </div>
          <p className="text-gray-600 mb-4">
            This is an official IELTS test result. For verification purposes, please use the result ID: {result.id}
          </p>
          {result.certificateSerial && (
            <p className="text-sm text-gray-500">
              Certificate Serial Number: {result.certificateSerial}
            </p>
          )}
          <div className="mt-4 flex justify-center space-x-4">
            <Link
              href="/search"
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Search Other Results
            </Link>
            {result.certificateSerial && (
              <Link
                href={`/verify/${result.certificateSerial}`}
                className="text-green-600 hover:text-green-700 text-sm font-medium"
              >
                Verify Certificate
              </Link>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}