import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { aiFeedback, testResults } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ resultId: string }> }
) {
  try {
    const { resultId } = await params;

    // Validate result ID format
    if (!resultId || resultId.trim() === '') {
      return NextResponse.json(
        { error: 'Invalid result ID format' },
        { status: 400 }
      );
    }

    // First check if the test result exists and is accessible
    const testResult = await db
      .select({
        id: testResults.id,
        status: testResults.status,
        aiFeedbackGenerated: testResults.aiFeedbackGenerated,
      })
      .from(testResults)
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!testResult.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    // Only return feedback for completed or verified results (not pending)
    if (testResult[0].status === 'pending') {
      return NextResponse.json(
        { error: 'Test result is not yet available' },
        { status: 403 }
      );
    }

    // Check if AI feedback has been generated
    if (!testResult[0].aiFeedbackGenerated) {
      return NextResponse.json(
        { error: 'AI feedback has not been generated for this result' },
        { status: 404 }
      );
    }

    // Get AI feedback - PUBLIC ACCESS (no authentication required)
    const feedback = await db
      .select({
        id: aiFeedback.id,
        testResultId: aiFeedback.testResultId,
        listeningFeedback: aiFeedback.listeningFeedback,
        readingFeedback: aiFeedback.readingFeedback,
        writingFeedback: aiFeedback.writingFeedback,
        speakingFeedback: aiFeedback.speakingFeedback,
        overallFeedback: aiFeedback.overallFeedback,
        studyRecommendations: aiFeedback.studyRecommendations,
        strengths: aiFeedback.strengths,
        weaknesses: aiFeedback.weaknesses,
        studyPlan: aiFeedback.studyPlan,
        generatedAt: aiFeedback.generatedAt,
      })
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, resultId))
      .limit(1);

    if (!feedback.length) {
      return NextResponse.json(
        { error: 'AI feedback not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(feedback[0]);

  } catch (error) {
    console.error('Error fetching public AI feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
