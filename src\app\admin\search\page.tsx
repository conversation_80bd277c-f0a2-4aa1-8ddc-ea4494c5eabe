'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Calendar,
  MapPin,
  User,
  FileText,
  BarChart3,
  RefreshCw
} from 'lucide-react';

interface SearchFilters {
  query: string;
  searchType: 'name' | 'email' | 'passport' | 'all';
  testCenter: string;
  testDateFrom: string;
  testDateTo: string;
  nationality: string;
  hasResults: 'all' | 'yes' | 'no';
  resultStatus: 'all' | 'pending' | 'completed' | 'verified';
  bandScoreMin: string;
  bandScoreMax: string;
}

interface SearchResult {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  passportNumber: string;
  nationality: string;
  testDate: string;
  testCenter: string;
  photoUrl?: string;
  hasResults: boolean;
  result?: {
    id: string;
    overallBandScore: number | null;
    status: string;
    createdAt: string;
  };
}

export default function AdminSearchPage() {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    searchType: 'all',
    testCenter: '',
    testDateFrom: '',
    testDateTo: '',
    nationality: '',
    hasResults: 'all',
    resultStatus: 'all',
    bandScoreMin: '',
    bandScoreMax: '',
  });

  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  const testCenters = [
    'Innovative Centre - Samarkand',
  ];

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSearch = async (e?: React.FormEvent) => {
    e?.preventDefault();
    setIsLoading(true);
    setHasSearched(true);

    try {
      const response = await fetch('/api/admin/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      });

      if (response.ok) {
        const data = await response.json();
        setResults(data.results);
        setTotalResults(data.total);
      } else {
        setResults([]);
        setTotalResults(0);
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
      setTotalResults(0);
    } finally {
      setIsLoading(false);
    }
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      searchType: 'all',
      testCenter: '',
      testDateFrom: '',
      testDateTo: '',
      nationality: '',
      hasResults: 'all',
      resultStatus: 'all',
      bandScoreMin: '',
      bandScoreMax: '',
    });
    setResults([]);
    setHasSearched(false);
  };

  const exportResults = async () => {
    try {
      const response = await fetch('/api/admin/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filters, format: 'csv' }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ielts_search_results_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Advanced Search</h1>
          <p className="text-gray-600">Search and filter candidates and test results</p>
        </div>
        {results.length > 0 && (
          <button
            onClick={exportResults}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Results
          </button>
        )}
      </div>

      {/* Search Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSearch} className="space-y-6">
          {/* Basic Search */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="searchType" className="block text-sm font-medium text-gray-700 mb-2">
                Search Type
              </label>
              <select
                id="searchType"
                value={filters.searchType}
                onChange={(e) => handleFilterChange('searchType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Fields</option>
                <option value="name">Full Name</option>
                <option value="email">Email</option>
                <option value="passport">Passport Number</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label htmlFor="query" className="block text-sm font-medium text-gray-700 mb-2">
                Search Query
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  id="query"
                  value={filters.query}
                  onChange={(e) => handleFilterChange('query', e.target.value)}
                  placeholder="Enter search term..."
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Advanced Filters Toggle */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
            >
              <Filter className="h-4 w-4 mr-1" />
              {showAdvanced ? 'Hide' : 'Show'} Advanced Filters
            </button>

            <div className="flex gap-2">
              <button
                type="button"
                onClick={clearFilters}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <RefreshCw className="h-4 w-4 mr-2 inline" />
                Clear
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvanced && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
              <div>
                <label htmlFor="testCenter" className="block text-sm font-medium text-gray-700 mb-2">
                  Test Center
                </label>
                <select
                  id="testCenter"
                  value={filters.testCenter}
                  onChange={(e) => handleFilterChange('testCenter', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Centers</option>
                  {testCenters.map((center) => (
                    <option key={center} value={center}>
                      {center}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="testDateFrom" className="block text-sm font-medium text-gray-700 mb-2">
                  Test Date From
                </label>
                <input
                  type="date"
                  id="testDateFrom"
                  value={filters.testDateFrom}
                  onChange={(e) => handleFilterChange('testDateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="testDateTo" className="block text-sm font-medium text-gray-700 mb-2">
                  Test Date To
                </label>
                <input
                  type="date"
                  id="testDateTo"
                  value={filters.testDateTo}
                  onChange={(e) => handleFilterChange('testDateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="nationality" className="block text-sm font-medium text-gray-700 mb-2">
                  Nationality
                </label>
                <input
                  type="text"
                  id="nationality"
                  value={filters.nationality}
                  onChange={(e) => handleFilterChange('nationality', e.target.value)}
                  placeholder="e.g., British, American"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="hasResults" className="block text-sm font-medium text-gray-700 mb-2">
                  Has Test Results
                </label>
                <select
                  id="hasResults"
                  value={filters.hasResults}
                  onChange={(e) => handleFilterChange('hasResults', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Candidates</option>
                  <option value="yes">With Results</option>
                  <option value="no">Without Results</option>
                </select>
              </div>

              <div>
                <label htmlFor="resultStatus" className="block text-sm font-medium text-gray-700 mb-2">
                  Result Status
                </label>
                <select
                  id="resultStatus"
                  value={filters.resultStatus}
                  onChange={(e) => handleFilterChange('resultStatus', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="verified">Verified</option>
                </select>
              </div>

              <div>
                <label htmlFor="bandScoreMin" className="block text-sm font-medium text-gray-700 mb-2">
                  Min Band Score
                </label>
                <input
                  type="number"
                  id="bandScoreMin"
                  value={filters.bandScoreMin}
                  onChange={(e) => handleFilterChange('bandScoreMin', e.target.value)}
                  min="1"
                  max="9"
                  step="0.5"
                  placeholder="1.0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="bandScoreMax" className="block text-sm font-medium text-gray-700 mb-2">
                  Max Band Score
                </label>
                <input
                  type="number"
                  id="bandScoreMax"
                  value={filters.bandScoreMax}
                  onChange={(e) => handleFilterChange('bandScoreMax', e.target.value)}
                  min="1"
                  max="9"
                  step="0.5"
                  placeholder="9.0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}
        </form>
      </div>

      {/* Search Results */}
      {hasSearched && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">
                Search Results {totalResults > 0 && `(${totalResults})`}
              </h3>
              {results.length > 0 && (
                <div className="flex items-center text-sm text-gray-500">
                  <BarChart3 className="h-4 w-4 mr-1" />
                  {results.filter(r => r.hasResults).length} with results
                </div>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Searching...</span>
            </div>
          ) : results.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {results.map((result) => (
                <div key={result.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {result.photoUrl ? (
                        <Image
                          className="h-12 w-12 rounded-full object-cover"
                          src={result.photoUrl}
                          alt={result.fullName}
                          width={48}
                          height={48}
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                          <User className="h-6 w-6 text-gray-400" />
                        </div>
                      )}

                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-gray-900">{result.fullName}</h4>
                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <FileText className="h-4 w-4 mr-1" />
                            {result.passportNumber}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(result.testDate).toLocaleDateString()}
                          </span>
                          <span className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {result.testCenter}
                          </span>
                        </div>
                        <div className="mt-1 text-sm text-gray-600">
                          {result.email} • {result.nationality}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      {result.hasResults && result.result ? (
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">
                            Band: {result.result.overallBandScore || 'N/A'}
                          </p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            result.result.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : result.result.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {result.result.status}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">No results</span>
                      )}

                      <div className="flex gap-2">
                        <Link
                          href={`/admin/candidates/${result.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                        <Link
                          href={`/admin/candidates/${result.id}/edit`}
                          className="text-green-600 hover:text-green-900"
                          title="Edit Candidate"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-600">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
